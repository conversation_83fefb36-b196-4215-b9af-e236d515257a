#!/usr/bin/env python3
"""
Simple test script to verify the /status endpoint works correctly.
This helps ensure the health check will pass in Azure App Service.
"""

import sys
import os
import json
from datetime import datetime

# Add the src/app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'app'))

def test_status_endpoint():
    """Test the /status endpoint functionality"""
    try:
        # Import the FastAPI app (test import fix)
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'app'))

        from main import app
        from fastapi.testclient import TestClient
        
        # Create test client
        client = TestClient(app)
        
        print("🧪 Testing /status endpoint...")
        
        # Test /status endpoint
        response = client.get("/status")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        # Verify response
        if response.status_code == 200:
            data = response.json()
            required_fields = ["status", "service", "timestamp", "version"]
            
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                print(f"❌ Missing required fields: {missing_fields}")
                return False
            
            if data["status"] == "healthy":
                print("✅ /status endpoint is working correctly!")
                return True
            else:
                print(f"❌ Unexpected status: {data['status']}")
                return False
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("pip install fastapi uvicorn python-multipart")
        return False
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

def test_health_endpoint():
    """Test the /health endpoint functionality"""
    try:
        from main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        print("\n🧪 Testing /health endpoint...")
        
        response = client.get("/health")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "OK":
                print("✅ /health endpoint is working correctly!")
                return True
            else:
                print(f"❌ Unexpected status: {data.get('status')}")
                return False
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing /health endpoint: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Filtro Curricular Backend Health Endpoints")
    print("=" * 60)
    
    # Test both endpoints
    status_ok = test_status_endpoint()
    health_ok = test_health_endpoint()
    
    print("\n" + "=" * 60)
    if status_ok and health_ok:
        print("✅ All health check endpoints are working correctly!")
        print("The backend should now pass Azure App Service health checks.")
        sys.exit(0)
    else:
        print("❌ Some health check endpoints failed!")
        print("Please fix the issues before deploying to Azure.")
        sys.exit(1)
