# 🔐 Azure Key Vault Secrets Management Guide

This guide provides comprehensive instructions for managing secrets in the Filtro Curricular Azure deployment using Azure Key Vault.

## 📋 Overview

The Filtro Curricular application uses Azure Key Vault to securely store and manage sensitive configuration data, including:

- OpenAI API credentials
- Assistant IDs for AI functionality
- Storage account keys (auto-generated)
- Application Insights keys (auto-generated)

## 🚀 Quick Start

### 1. **Set Up OpenAI Secrets (Required)**

```bash
# Navigate to the infrastructure directory
cd apps/infrastructure/azure/filtro-curricular

# Run the interactive secrets setup
./scripts/manage-secrets.sh update
```

### 2. **Verify Deployment**

```bash
# Run full deployment verification
./scripts/verify-deployment.sh

# Or check specific components
./scripts/verify-deployment.sh secrets
./scripts/verify-deployment.sh health
```

## 🔧 Detailed Setup Instructions

### **Method 1: Interactive Setup (Recommended)**

```bash
# Update all OpenAI secrets interactively
./scripts/manage-secrets.sh update
```

This will prompt you to enter:
- OpenAI API Key (starts with `sk-proj-` or `sk-`)
- <PERSON><PERSON><PERSON> Token (starts with `sk-`)
- Assistant ID for Juridico (starts with `asst_`)
- Assistant ID for Calidad (starts with `asst_`)

### **Method 2: From File**

1. **Create secrets file:**
   ```bash
   cp secrets/secrets.json.example secrets/secrets.json
   ```

2. **Edit the file with your actual credentials:**
   ```json
   {
     "openai_api_key": "sk-proj-your-actual-api-key-here",
     "openai_token": "sk-your-actual-token-here",
     "assistant_id_juridico": "asst_your-juridico-assistant-id",
     "assistant_id_calidad": "asst_your-calidad-assistant-id"
   }
   ```

3. **Upload to Key Vault:**
   ```bash
   ./scripts/manage-secrets.sh update-from-file secrets/secrets.json
   ```

4. **Delete the local file:**
   ```bash
   rm secrets/secrets.json
   ```

### **Method 3: Azure CLI Direct**

```bash
# Get Key Vault name
KEY_VAULT_NAME=$(az keyvault list --resource-group filtro-curricular-rg-dev --query "[0].name" -o tsv)

# Set individual secrets
az keyvault secret set --vault-name "$KEY_VAULT_NAME" --name "openai-api-key" --value "sk-proj-your-key"
az keyvault secret set --vault-name "$KEY_VAULT_NAME" --name "openai-token" --value "sk-your-token"
az keyvault secret set --vault-name "$KEY_VAULT_NAME" --name "assistant-id-juridico" --value "asst_your-id"
az keyvault secret set --vault-name "$KEY_VAULT_NAME" --name "assistant-id-calidad" --value "asst_your-id"
```

## 🔍 Verification and Troubleshooting

### **Check Secret Status**

```bash
# List all secrets
./scripts/manage-secrets.sh list

# Get info about specific secret
./scripts/manage-secrets.sh info openai-api-key

# Verify deployment health
./scripts/verify-deployment.sh
```

### **Common Issues and Solutions**

#### **Issue: App Service Can't Access Key Vault**

**Symptoms:**
- Health checks fail
- Application logs show "mock" OpenAI clients
- App settings show Key Vault references but values aren't resolved

**Solution:**
```bash
# Check managed identity
az webapp identity show --name filtro-curricular-be-dev --resource-group filtro-curricular-rg-dev

# Verify Key Vault access policy
KEY_VAULT_NAME=$(az keyvault list --resource-group filtro-curricular-rg-dev --query "[0].name" -o tsv)
az keyvault show --name "$KEY_VAULT_NAME" --query "properties.accessPolicies"

# Restart app service
az webapp restart --name filtro-curricular-be-dev --resource-group filtro-curricular-rg-dev
```

#### **Issue: Placeholder Values in Secrets**

**Symptoms:**
- Secrets exist but contain "your-*-here" values
- Application uses mock responses

**Solution:**
```bash
# Update secrets with real values
./scripts/manage-secrets.sh update

# Restart app service to pick up changes
az webapp restart --name filtro-curricular-be-dev --resource-group filtro-curricular-rg-dev
```

#### **Issue: Health Check Timeouts**

**Symptoms:**
- GitHub Actions deployment fails at health check step
- `/status` endpoint doesn't respond

**Solution:**
```bash
# Check application logs
az webapp log tail --name filtro-curricular-be-dev --resource-group filtro-curricular-rg-dev

# Test endpoints manually
curl -v https://filtro-curricular-be-dev.azurewebsites.net/status
curl -v https://filtro-curricular-be-dev.azurewebsites.net/health

# Check container startup
./scripts/verify-deployment.sh logs
```

## 📊 Monitoring and Maintenance

### **Regular Checks**

```bash
# Weekly verification
./scripts/verify-deployment.sh

# Check for expiring secrets (if applicable)
./scripts/manage-secrets.sh list
```

### **Application Insights**

Monitor your application through Azure Portal:
1. Go to **Application Insights** → `filtro-curricular-insights-dev`
2. Check **Live Metrics** for real-time monitoring
3. Review **Failures** for any Key Vault access issues
4. Use **Logs** to query specific events

### **Useful KQL Queries**

```kusto
// Check OpenAI API calls
requests
| where url contains "/api/"
| project timestamp, url, resultCode, duration
| order by timestamp desc

// Look for Key Vault access errors
traces
| where message contains "KeyVault" or message contains "secret"
| project timestamp, message, severityLevel
| order by timestamp desc
```

## 🔒 Security Best Practices

### **Do's:**
- ✅ Use the provided scripts for secret management
- ✅ Delete local secret files after uploading
- ✅ Regularly rotate OpenAI API keys
- ✅ Monitor Key Vault access logs
- ✅ Use managed identities for authentication

### **Don'ts:**
- ❌ Never commit actual secrets to version control
- ❌ Don't share Key Vault access keys
- ❌ Don't store secrets in plain text files
- ❌ Don't use placeholder values in production

## 📚 Reference

### **Required Secrets**

| Secret Name | Format | Description | App Setting |
|-------------|--------|-------------|-------------|
| `openai-api-key` | `sk-proj-[64-chars]` | OpenAI API Key for GPT models | `GPT_API_KEY` |
| `openai-token` | `sk-[64-chars]` | OpenAI Token for Assistant API | `OPENAI_TOKEN` |
| `assistant-id-juridico` | `asst_[24-chars]` | Assistant ID for Juridico domain | `ASSISTANT_ID_JURIDICO` |
| `assistant-id-calidad` | `asst_[24-chars]` | Assistant ID for Calidad domain | `ASSISTANT_ID_CALIDAD` |

### **Auto-Generated Secrets**

These are automatically created by Terraform:
- `storage-connection-string`
- `storage-account-key`

### **Azure Resources**

- **Key Vault**: `filtro-kv-dev-[suffix]`
- **Backend App Service**: `filtro-curricular-be-dev`
- **Frontend App Service**: `filtro-curricular-fe-dev`
- **Resource Group**: `filtro-curricular-rg-dev`

## 🆘 Getting Help

If you encounter issues:

1. **Run diagnostics:**
   ```bash
   ./scripts/verify-deployment.sh troubleshoot
   ```

2. **Check logs:**
   ```bash
   ./scripts/verify-deployment.sh logs
   ```

3. **Verify secrets:**
   ```bash
   ./scripts/verify-deployment.sh secrets
   ```

4. **Test health endpoints:**
   ```bash
   ./scripts/verify-deployment.sh health
   ```

For additional support, check the Azure Portal logs and Application Insights for detailed error information.
